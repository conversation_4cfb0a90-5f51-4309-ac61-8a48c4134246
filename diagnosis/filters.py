from core.filters import SearchMixin
from diagnosis.models import Diagnosis
from django_filters import rest_framework as filters


class DiagnosisFilter(SearchMixin):
    search_fields = ["gdrg", "description", "name", "icd_code"]

    search = filters.Char<PERSON>ilter(
        method="filter_search", label="Search", help_text=f"search for {search_fields}"
    )

    name = filters.CharFilter(field_name="name", lookup_expr="icontains")

    class Meta:
        model = Diagnosis
        fields = [
            "speciality_attended",
            "gdrg",
            "name",
            "description",
            "icd_code",
            "search",
            "is_facility_diagnosis",
        ]
