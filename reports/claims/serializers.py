import datetime
import logging

from admission.models import Admission
from choice.views import serviceOutcomeChoices
from consultation_diagnosis.serializers import ConsultationDiagnosis
from consultation_diagnosis.serializers import ConsultationDiagnosisDiagnosis
from daily_attendance.serializers import DailyAttendanceModel
from dispense_medicine.serializers import DispenseMedicine
from django.db.models import Min
from django.db.models import Sum
from django.db.models.functions import Coalesce
from lab_bill_details.models import LabBllDetails
from lab_bill_details.serializers import LabbillSerializer
from patient_discharge.models import PatientDischarge
from person.models import Patient
from prescription.models import Prescription
from prescription.serializers import WarehouseProductSerializer
from procedure.serializers import ProcedureSerializer
from procedure_bill_details.serializers import ProcedureBillDetails
from radiology_bill_detils.models import RadiologyBillDetils
from radiology_bill_detils.serializers import RadiologySerializer
from rest_framework import serializers
from service.models import Service
from service_bill.models import ServiceBill
from sponsor_patient.serializers import SponsorPatientSerializer
from validator.views import EncryptionMixin

encryptionMixin = EncryptionMixin()


logger = logging.getLogger(__name__)


class AbstractPatientNameSerializer(serializers.ModelSerializer):
    date_of_birth = serializers.CharField(source="user.date_of_birth")
    username = serializers.CharField(source="user.username")
    gender = serializers.CharField(source="user.gender", read_only=True)
    national_id = serializers.CharField(source="user.national_id", read_only=False)
    first_name = serializers.CharField(source="user.first_name", read_only=False)
    last_name = serializers.CharField(source="user.last_name", read_only=False)
    middle_name = serializers.CharField(source="user.middle_name", read_only=False)
    email = serializers.CharField(source="user.email")
    email = serializers.CharField(source="user.email")
    client_id = serializers.IntegerField(source="user.id", help_text="Patient ID")

    class Meta:
        model = Patient
        fields = (
            "client_id",
            "username",
            "first_name",
            "last_name",
            "middle_name",
            "email",
            "hospital_record_no",
            "age",
            "full_name",
            "title",
            "date_of_birth",
            "gender",
            "national_id",
        )


class ClaimsDailyAttendanceSerializer(serializers.ModelSerializer):
    patient = AbstractPatientNameSerializer()

    class Meta:
        model = DailyAttendanceModel
        fields = "__all__"


class ClientInfomatonSerializer(serializers.ModelSerializer):
    patient_attendance = ClaimsDailyAttendanceSerializer(many=True, read_only=True)

    patient_sponsor = SponsorPatientSerializer(many=True, read_only=True)

    class Meta:
        model = Patient
        fields = (
            "patient_attendance",
            "patient_sponsor",
        )


# class ServicesProvidedSerializer(serializers.ModelSerializer):
#     services_provided = CombinedPatientServiceChargeSerializer(
#         many=True, read_only=True, source="patient.patiant_service_charges"
#     )

#     class Meta:
#         model = DailyAttendanceModel
#         fields = [
#             "services_provided",
#             #   "patientdischarge",
#         ]

#     # def get_num_of_visits(self, obj):
#     #     count = obj.patiant_service_charges.count()
#     #     return count  # 1 if count <= 0 else count


class ServicesProvidedSerializer(serializers.ModelSerializer):
    patient_id = serializers.CharField(source="patient.id")
    full_name = serializers.CharField(source="patient.full_name")
    attendance_id = serializers.IntegerField(source="id")
    attendance_date = serializers.DateTimeField(source="date_of_visit")
    treatment_outcome = serializers.SerializerMethodField()
    service_type = serializers.SerializerMethodField()
    date_time = serializers.DateTimeField(source="created_on")
    service_category = serializers.CharField(source="attendance_type.name")
    attendance_type = serializers.CharField(source="attendance_type.code")
    specialty_code = serializers.CharField(source="clinic.code", allow_null=True)
    duration_of_spell = serializers.SerializerMethodField()

    class Meta:
        model = DailyAttendanceModel
        fields = [
            "id",
            "patient_id",
            "full_name",
            "service_type",
            "attendance_id",
            "attendance_date",
            "treatment_outcome",
            "date_time",
            "service_category",
            "attendance_type",
            "specialty_code",
            "duration_of_spell",
        ]

    def get_treatment_outcome(self, obj):
        # Fetch treatment outcome from the related PatientDischarge model
        discharge = PatientDischarge.objects.filter(attendance=obj).first()
        if discharge:
            return discharge.treatment_outcome
        return "Not Available"

    def get_service_type(self, obj):
        # Fetch service types provided in this attendance
        services = obj.patient.patiant_service_charges.values_list(
            "service__name", flat=True
        )
        return ", ".join(services)

    def get_duration_of_spell(self, obj):
        # Calculate the duration of spell from first attendance to modified_on date
        first_date = DailyAttendanceModel.objects.filter(patient=obj.patient).aggregate(
            first_date=Min("date_of_visit")
        )["first_date"]
        if first_date:
            duration = obj.modified_on - first_date
            return str(duration)
        return "Not Available"


class diagnosisSerializer(serializers.ModelSerializer):
    diagnosis = serializers.SerializerMethodField()
    # client_id  = serializers.IntegerField(source='id')
    # doctor =  serializers.SerializerMethodField()

    class Meta:
        model = Patient
        fields = [
            "diagnosis",
        ]

    def get_diagnosis(self, obj):
        queryset = (
            ConsultationDiagnosisDiagnosis.objects.select_related(
                "consultation_diagnosis", "diagnosis"
            )
            .exclude(diagnosis__isnull=True)
            .filter(
                consultation_diagnosis__consultation__vital_sign__attendance__patient=obj
            )
            .values(
                "consultation_diagnosis__created_by",
                "consultation_diagnosis__created_by__username",
                "created_on",
                "diagnosis__name",
                "diagnosis__description",
                "diagnosis__gdrg",
                "diagnosis__icd_code",
            )
        )

        data = []
        for item in queryset:
            data.append(
                {
                    "doctor": {
                        "id": item["consultation_diagnosis__created_by"],
                        "name": item["consultation_diagnosis__created_by__username"],
                    },
                    "client_id": obj.id,
                    "name": item["diagnosis__name"],
                    "icd_code": item["diagnosis__icd_code"],
                    "description": item["diagnosis__description"],
                    "gdrg": item["diagnosis__gdrg"],
                    "created_on": item["created_on"],
                }
            )
        return data


class ClaimsProcedureSerializer(serializers.ModelSerializer):
    procedure = ProcedureSerializer(many=False)

    class Meta:
        model = ProcedureBillDetails
        fields = ["procedure"]

    def to_representation(self, instance):
        representation = super().to_representation(instance)
        patient_id = instance.service_bill.attendance.patient.id
        representation["procedure"]["patient_id"] = patient_id
        return representation


class ProceduresSerializer(serializers.ModelSerializer):
    procedures = serializers.SerializerMethodField()

    class Meta:
        model = Patient
        fields = ["procedures"]

    def get_procedures(self, obj):
        procedure_bill_details = (
            ProcedureBillDetails.objects.select_related("service_bill")
            .only("service_bill", "id")
            .filter(service_bill__attendance__patient=obj)
        )

        serializer = ClaimsProcedureSerializer(procedure_bill_details, many=True)
        return serializer.data


# class ClaimsSummarySerializer(serializers.ModelSerializer):

#     class Meta:
#         model = Patient
#         fields = "__all__"


class ClaimsDispenseMedicineSerializer(serializers.ModelSerializer):
    warehouse_products = WarehouseProductSerializer(
        source="warehouse_product", read_only=True, required=False
    )
    warehouse_stock = serializers.SerializerMethodField()
    date = serializers.SerializerMethodField()
    # total_cost = serializers.SerializerMethodField()

    class Meta:
        model = DispenseMedicine
        fields = [
            "date",
            "warehouse_products",
            "warehouse_stock",
        ]

    def get_date(self, obj):
        created_at = obj.created_at.strftime("%A, %d %B %Y %I:%M %p")
        return created_at

    def get_warehouse_stock(self, obj):
        data = obj.warehouse_product.warehouse_product_id_in_stock.values(
            "quantity", "cost_price", "reorder_level"
        ).annotate(total_cost=Sum("cost_price") * Sum("quantity"))

        return data

    # def get_total_cost(self, obj):
    #     data = obj.warehouse_product.warehouse_product_id_in_stock.aggregate(
    #         total_cost=Sum(F('cost_price') * F('quantity'))
    #     )
    #     return data.get('total_cost')


class MedicineSerializer(serializers.ModelSerializer):
    dispence_medicines = serializers.SerializerMethodField()
    patient_id = serializers.IntegerField(source="id")

    class Meta:
        model = Patient

        fields = [
            "patient_id",
            "dispence_medicines",
        ]

    def get_dispence_medicines(self, obj):
        dispence_medicines = DispenseMedicine.objects.select_related(
            "pharmacy_bill_details", "warehouse_product"
        ).filter(pharmacy_bill_details__service_bill__attendance__patient=obj)

        serializer = ClaimsDispenseMedicineSerializer(dispence_medicines, many=True)
        return serializer.data


#    NOT USING THIS


class ClientClaimsSummarySerializer(serializers.ModelSerializer):
    patient_id = serializers.IntegerField(source="attendance.patient.id")

    class Meta:
        model = ServiceBill
        fields = ["patient_id"]

    def get_date_of_visit(self, obj):
        date_of_visit = obj.attendance.date_of_visit.strftime("%A, %d %B %Y %I:%M %p")
        return date_of_visit

    def get_opd_bill_payment(self, obj):
        opdbills = obj.payment_of_patient_opdbill.all()
        total_price = opdbills.aggregate(
            total_price=Coalesce(Sum("payment_to_opdbill__patient_price"), 0)
            + Coalesce(Sum("payment_to_opdbill__sponsor_price"), 0)
        )
        # print('total_price', total_price)
        return total_price.get("total_price")

    def get_pharmacy_bill_payment(self, obj):
        opdbills = obj.payment_of_patient_pharmacybill.all()
        total_price = opdbills.aggregate(
            total_price=Coalesce(Sum("payment_to_pharmacy_bill__patient_price"), 0)
            + Coalesce(Sum("payment_to_pharmacy_bill__sponsor_price"), 0)
        )
        # print('total_price', total_price)
        return total_price.get("total_price")


class ClientClaimServiceTypeSerializer(serializers.ModelSerializer):
    service_type = serializers.CharField(source="service_type.name")
    gdrg_code = serializers.CharField(source="gdrgCode")
    tariff_amount = serializers.DecimalField(max_digits=10, decimal_places=2)
    date_time = serializers.DateTimeField(source="created_on")
    claims_officer = serializers.CharField(
        source="created_by.username"
    )  # Assuming 'created_by' is Claims Officer
    signature = serializers.SerializerMethodField()

    class Meta:
        model = Service
        fields = [
            "id",
            "service_type",
            "gdrg_code",
            "tariff_amount",
            "date_time",
            "claims_officer",
            "signature",
        ]

    def get_signature(self, obj):
        """
        Custom method to return the last name of the user who created the record.
        """
        # Returning the last name of the user who created the record
        return f"{obj.created_by.username}" if obj.created_by else "Not Available"


# class InvestigationsSerializer(serializers.ModelSerializer):
#     lab = LabbillSerializer(source="payment_of_patient_labbill", many=True)
#     radilogy = RadiologySerializer(source="payment_of_patient_radiologybill", many=True)
#     patient_id = serializers.IntegerField(source="attendance.patient.id")

#     class Meta:
#         model = ServiceBill
#         fields = ["patient_id", "lab", "radilogy"]


class InvestigationsSerializer(serializers.ModelSerializer):
    # Added fields to reflect the required structure
    patient_id = serializers.IntegerField(source="attendance.patient.id")
    investigation_id = serializers.IntegerField(
        source="id"
    )  # Corresponds to the investigation ID
    investigation_type = serializers.CharField(
        source="service.type"
    )  # Assuming 'service' has a 'type' field
    investigation_lab_name = serializers.CharField(
        source="payment_of_patient_labbill.lab_test.name"
    )  # Assuming lab test has a 'name'
    investigation_lab_description = serializers.CharField(
        source="payment_of_patient_labbill.lab_test.description"
    )  # Assuming lab test has a 'description'
    date_time = serializers.DateTimeField(
        source="created_on"
    )  # Date/Time when the investigation occurred
    # Assuming 'drg' is part of the lab test
    g_drg = serializers.CharField(source="payment_of_patient_labbill.lab_test.drg")

    class Meta:
        model = ServiceBill
        fields = [
            "patient_id",
            "investigation_id",
            "investigation_type",
            "investigation_lab_name",
            "investigation_lab_description",
            "date_time",
            "g_drg",
        ]


class TreatmentOutcomeSerializer(serializers.ModelSerializer):
    class Meta:
        model = DailyAttendanceModel
        exclude = ["created_on", "modified_on"]


class ClaimsSummarySerializer(serializers.Serializer):
    service = serializers.CharField(source="name")
    ipd_total = serializers.CharField()
    opd_total = serializers.CharField()
    pharmacy_total = serializers.CharField()
    radiology_total = serializers.CharField()
    procedure_total = serializers.CharField()
    lab_total = serializers.CharField()


class NHISInvestigationsSerializer(serializers.ModelSerializer):
    lab = LabbillSerializer(source="payment_of_patient_labbill", many=True)
    radilogy = RadiologySerializer(source="payment_of_patient_radiologybill", many=True)
    patient_id = serializers.IntegerField(source="attendance.patient.id")

    class Meta:
        model = ServiceBill
        fields = ["patient_id", "lab", "radilogy"]


class NHISClaimsSerialize(serializers.Serializer):
    claimID = serializers.IntegerField(source="id")
    claimCheckCode = serializers.SerializerMethodField()
    physicianID = serializers.SerializerMethodField()
    preAuthorizationCodes = serializers.SerializerMethodField()
    memberNo = serializers.SerializerMethodField()
    isDependant = serializers.SerializerMethodField()
    typeOfService = serializers.SerializerMethodField()
    typeOfAttendance = serializers.StringRelatedField(source="attendance_type.code")
    cardSerialNo = serializers.SerializerMethodField()
    surname = serializers.CharField(source="patient.user.last_name")
    folderNumber = serializers.IntegerField(source="id")
    otherNames = serializers.SerializerMethodField()
    dateOfBirth = serializers.DateField(source="patient.user.date_of_birth")
    age = serializers.IntegerField(source="patient.age")
    gender = serializers.SerializerMethodField()
    hospitalRecNo = serializers.DateField(source="patient.hospital_record_no")
    dateOfService = serializers.SerializerMethodField()
    isUnbundled = serializers.SerializerMethodField()
    includesPharmacy = serializers.SerializerMethodField()

    serviceOutcome = serializers.SerializerMethodField()

    investigation = serializers.SerializerMethodField()
    procedure = serializers.SerializerMethodField()
    diagnosis = serializers.SerializerMethodField()
    medicine = serializers.SerializerMethodField()

    specialtyAttended = serializers.SerializerMethodField()

    referralInfo = serializers.SerializerMethodField()

    def get_claimCheckCode(self, obj: DailyAttendanceModel):
        return str(obj.ccc)

    def get_gender(self, obj):
        if obj.patient.user.gender == "male":
            return "M"
        elif obj.patient.user.gender == "female":
            return "F"

    def get_memberNo(self, obj):
        sponsor_patient = obj.patient.patient_sponsor.last()

        if sponsor_patient:
            return sponsor_patient.memberId
        return None

    def get_physicianID(self, obj: DailyAttendanceModel):
        data = obj.vital_sign_patient.order_by("-created_on").first()
        if data:
            physicianID = data.cons_vital.values_list("created_by", flat=True).first()
            return physicianID
        return None

    def get_isDependant(self, obj: DailyAttendanceModel):
        return 1 if obj.is_dependant else 0

    def get_isUnbundled(self, obj: DailyAttendanceModel):
        date_of_birth = obj.patient.user.date_of_birth

        # Calculate a date 3 months from now
        three_months_from_now = datetime.date.today() + datetime.timedelta(days=90)

        if date_of_birth >= three_months_from_now:
            return 1  # Date of birth is within 3 months
        else:
            return 0  # Date of birth is more than 3 months away

    def get_typeOfService(self, obj: DailyAttendanceModel):
        consultation_diagnosis = (
            ConsultationDiagnosis.objects.filter(vital_sign__attendance=obj)
            .select_related("consultation__service__service_type")
            .order_by("-created_on")
            .first()
        )

        if (
            consultation_diagnosis
            and consultation_diagnosis.consultation
            and consultation_diagnosis.consultation.service
        ):
            return consultation_diagnosis.consultation.service.service_type.code

        return None

    def get_dateOfService(self, obj: DailyAttendanceModel):
        date_of_service = []

        # Add the date of visit to the list if it exists
        if obj.date_of_visit:
            date_of_service.append(obj.date_of_visit.date())

        service_type = self.get_typeOfService(obj)

        if service_type in ["OPD", "PHC"]:
            try:
                opd_ipd_date = (
                    ConsultationDiagnosis.objects.order_by("-created_on")
                    .filter(vital_sign__attendance=obj)
                    .first()
                    .created_on
                )
                date_of_service.append(opd_ipd_date.date())
            except Exception as e:
                logger.error(
                    f"Error fetching created_on for {service_type} service: {str(e)}"
                )
        elif service_type == "IPD":
            try:
                patient_discharge = PatientDischarge.objects.filter(
                    attendance=obj
                ).first()
                if not patient_discharge:
                    return
                date_of_service.append(patient_discharge.discharge_date.date())
            except Exception as e:
                logger.error(f"Error IPD No Discharge Date Provided: {str(e)}")

        # Alway s return date_of_visit if it exists, regardless of the service type

        return date_of_service

    def get_serviceOutcome(self, obj: DailyAttendanceModel):
        service_type = self.get_typeOfService(obj)

        if service_type != "IPD":
            return serviceOutcomeChoices.Discharged

        treatment_outcome = Admission.objects.filter(
            consultation_diagnosis__vital_sign__attendance=obj
        ).first()

        if treatment_outcome:
            return treatment_outcome.treatment_outcome

        return None

    def get_cardSerialNo(self, obj: DailyAttendanceModel):
        sponsor_patient = obj.patient.patient_sponsor.order_by("-created_on").first()

        if sponsor_patient:
            return sponsor_patient.memberId or sponsor_patient.card_serial_no
        return None

    def get_otherNames(self, obj: DailyAttendanceModel):
        first_name = obj.patient.user.first_name or ""
        middle_name = obj.patient.user.middle_name or ""
        otherNames = f"{first_name} {middle_name}"
        return otherNames

    def get_investigation(self, obj: DailyAttendanceModel):
        data = []

        service_bills = ServiceBill.objects.filter(attendance=obj).first()

        if not service_bills:
            return None

        lab_queryset = (
            LabBllDetails.objects.filter(service_bill=service_bills)
            .prefetch_related("lab_test_items__lab_test")
            .exclude(lab_test_items__lab_test__drg__isnull=True)
            .exclude(lab_test_items__lab_test__drg="")
            .values("created_on__date", "lab_test_items__lab_test__drg")
        )

        for item in lab_queryset:
            data.append(
                {
                    "serviceDate": item["created_on__date"],
                    "gdrgCode": item["lab_test_items__lab_test__drg"],
                }
            )

        # Process RadiologyBillDetils
        radiology_queryset = (
            RadiologyBillDetils.objects.filter(service_bill=service_bills)
            .exclude(radiology_procedure__gdrg__isnull=True)
            .exclude(radiology_procedure__gdrg="")
            .values("created_at__date", "radiology_procedure__gdrg")
        )

        for item in radiology_queryset:
            data.append(
                {
                    "serviceDate": item["created_at__date"],
                    "gdrgCode": item["radiology_procedure__gdrg"],
                }
            )

        return data if data else None

    def get_specialtyAttended(self, obj):
        attended_specialty = (
            ConsultationDiagnosis.objects.filter(vital_sign__attendance=obj)
            .exclude(attended_specialty__code__isnull=True)
            .values_list("attended_specialty__code", flat=True)
        )

        return list(attended_specialty)

    def get_preAuthorizationCodes(self, obj: DailyAttendanceModel):
        pre_authorization_codes = (
            obj.patient.user.facility.pre_authorization_codes.values_list(
                "code", flat=True
            )
        )
        codes = ",".join(pre_authorization_codes)
        # .split(',')
        return codes

    def get_procedure(self, obj: ProcedureBillDetails):
        procedures = ProcedureBillDetails.objects.select_related(
            "service_bill", "consultation_diagnosis"
        ).filter(service_bill__attendance=obj, diagnosis__isnull=False)

        data = [
            {
                "serviceDate": procedure.created_on.date(),
                "gdrgCode": procedure.procedure.gdrg,
                "icd10": procedure.diagnosis.icd_code,
                "diagnosis": procedure.diagnosis.name,
            }
            for procedure in procedures
        ]

        return data or None

    def get_diagnosis(self, obj):
        queryset = (
            ConsultationDiagnosis.objects.select_related("consultation", "vital_sign")
            .filter(consultation__vital_sign__attendance=obj)
            .exclude(consultation_diagnoses__diagnosis__isnull=True)
            .exclude(consultation_diagnoses__diagnosis__is_facility_diagnosis=True)
            .values(
                "consultation_diagnoses__diagnosis__gdrg",
                "consultation_diagnoses__diagnosis__icd_code",
                "consultation_diagnoses__diagnosis__name",
            )
        )

        if queryset.exists():
            data = [
                {
                    "gdrgCode": item["consultation_diagnoses__diagnosis__gdrg"],
                    "icd10": item["consultation_diagnoses__diagnosis__icd_code"],
                    "diagnosis": item["consultation_diagnoses__diagnosis__name"],
                }
                for item in queryset
            ]
            return data

        # Return None if no records found.
        return None

    def get_referralInfo(self, obj: DailyAttendanceModel):
        facility_code = obj.patient.user.facility.facility_code
        facility_name = obj.patient.user.facility.name
        return {
            "facilityID": facility_code,
            "facilityName": facility_name,
            "claimCheckCode": obj.ccc,
        }

    def get_includesPharmacy(self, obj):
        return 1 if self.get_medicine(obj) is not None else 0

    def get_medicine(self, obj: DailyAttendanceModel):
        data = []

        prescriptions = Prescription.objects.filter(
            consultation_diagnosis__vital_sign__attendance=obj
        )

        for prescription in prescriptions:
            warehouse_product = prescription.warehouse_product

            duration = (
                f"{prescription.duration or 0} {prescription.duration_unit}".upper()
            )
            frequency = (
                f"{prescription.frequency or 0} {prescription.frequency_unit}".replace(
                    ",", ""
                ).upper()
            )
            dose = f"{prescription.dose} {warehouse_product.strength_unit or ''}"

            data.append(
                {
                    "name": warehouse_product.name,
                    "medicineCode": warehouse_product.icd_code,
                    "dispensedQty": prescription.quantity_to_dispense,
                    "serviceDate": prescription.prescription_date.date(),
                    "prescription": {
                        "dose": dose,
                        "frequency": frequency,
                        "duration": duration,
                        "unparsed": prescription.unparsed,
                    },
                }
            )

        # Return the collected data if any, otherwise return None
        return data if data else None
