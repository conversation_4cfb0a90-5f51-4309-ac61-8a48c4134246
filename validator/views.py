import hashlib
import logging

import crum
from Crypto.Cipher import AES
from django import forms
from django.conf import settings
from django.core.exceptions import ValidationError
from django.core.validators import RegexValidator
from django.utils import timezone
from rest_framework import serializers


try:
    import magic

    MAGIC_AVAILABLE = True
except ImportError:
    logging.error(
        "python-magic library not available. MIME type detection will be disabled."
    )
    MAGIC_AVAILABLE = False
    magic = None


# from crum import get_current_user

# current_user = get_current_user()
# has_id_attribute = hasattr(current_user, 'id') if current_user else False
# print(has_id_attribute)


class MyDateInput(forms.DateInput):
    input_type = "date"


class MyTimeInput(forms.TimeInput):
    input_type = "time"


class OptionalPhoneValidator(RegexValidator):
    def __call__(self, value):
        if value is not None and value != "":
            super().__call__(value)
        else:
            print(value)


valid_phone_number = OptionalPhoneValidator(
    regex=r"^\+?1?\d{9,15}$",
    message="Phone number must be entered in the format: '+233000000000'. "
    "Up to 15 digits allowed.",
)


def validate_file_size(file_field):
    file_size = file_field.size
    megabyte_limit = 1.00
    if file_size > megabyte_limit * 1024 * 1024:
        raise ValidationError("Max file size is %sMB" % str(megabyte_limit))


def validate_pdf(value):
    # Check if the file is a pdf file
    if not MAGIC_AVAILABLE:
        return
    mime = magic.Magic(mime=True)
    file_type = mime.from_buffer(value.read())
    if file_type != "application/pdf":
        raise serializers.ValidationError({"message": "Only pdf files are allowed"})


# def validate_pdf(value):
#     if not value.name.endswith('.pdf'):
#         raise serializers.ValidationError({'message':'Only pdf files are allowed'})


class EncryptionMixin:
    """ENCRYPT AND DECRYPT DATA OF MODEL INSTANCES USING SETTINGS SECRET KEY AS PASSWORD"""

    def _encrypt(self, value):
        if value:
            hash_object = hashlib.sha256(settings.SECRET_KEY.encode())
            salt = hash_object.digest()[:16]
            key = hashlib.pbkdf2_hmac(
                "sha256", settings.SECRET_KEY.encode(), salt, 100000
            )
            iv = hashlib.sha256(str.encode(value)).digest()[:16]
            cipher = AES.new(key, AES.MODE_CBC, iv)
            padded_value = self._pad(value.encode())
            encrypted_value = cipher.encrypt(padded_value)
            return salt + iv + encrypted_value

    def _decrypt(self, value):
        if value:
            value = bytes.fromhex(value)  # Convert hex string to bytes
            salt = value[:16]
            iv = value[16:32]
            encrypted_value = value[32:]
            hashlib.sha256(settings.SECRET_KEY.encode())
            key = hashlib.pbkdf2_hmac(
                "sha256", settings.SECRET_KEY.encode(), salt, 100000
            )
            cipher = AES.new(key, AES.MODE_CBC, iv)
            decrypted_value = cipher.decrypt(encrypted_value)
            unpadded_value = self._unpad(decrypted_value)
            return unpadded_value.decode()

    def _pad(self, s):
        bs = 16
        return s + (bs - len(s) % bs) * chr(bs - len(s) % bs).encode()

    def _unpad(self, s):
        return s[: -ord(s[len(s) - 1 :])]


def prevent_future_date_validator(value):
    """PREVENT SAVING DATE VALUE TO FUTURE DATE"""
    if value and value > timezone.now().date():
        raise serializers.ValidationError({"message": "future date not allowed"})


def prevent_past_date_validator(value):
    """PREVENT SAVING DATE VALUE TO PAST DATE"""
    if value and value < timezone.now().date():
        raise serializers.ValidationError({"message": "past date not allowed"})


def get_user_facility(user=None):
    """GET THE LOGGED IN USER FACILITY BY ID IF PRIVATE PRACTICE ELSE NONE"""
    current_user = crum.get_current_user()
    if current_user and hasattr(current_user, "id"):
        private_practice_user = current_user.group.name == "Private Practice"

        facility_id = current_user.facility.id if private_practice_user else None
        print(facility_id, private_practice_user)
        return facility_id
    return None


# def get_logged_in_user(user):
#     """GET THE LOGGED IN USER OBJECT"""
#     current_user = crum.get_current_user()
#     if current_user and hasattr(current_user, 'id'):
#         return current_user

#     return None


def filter_queryset_by_facility_user(queryset, facility_id):
    """GET USER BY FACILTITY"""
    if facility_id is None:
        return queryset.all()
    return queryset.filter(facility_id=facility_id)


def filter_queryset_by_facility_group(queryset, facility_id):
    """GET GROUP BY FACILITY"""
    if facility_id is None:
        return queryset.all()
    return queryset.filter(created_by__facility__id=facility_id)


# def get_user_facility():
#     current_user = crum.get_current_user()
#     if current_user and hasattr(current_user, 'id'):
#         private_practice_user = current_user.group.name=="Private Practice"
#         print('private_practice_user')

#         facility_id = current_user.facility.id if private_practice_user else None
#         print(facility_id,private_practice_user)
#         return facility_id
#     return None


# IP_ADDRESS = f'{settings.CACHE_KEY_PREFIX}:_ip_address'

# ip_address = cache.get(IP_ADDRESS)


# def private_private_user(faclity_id,pp_user):
#     """ GET FACILITY ID IF USER IS PP USER"""
#     return faclity_id if pp_user else None

# print('ip_address :',IP_ADDRESS)

# facility_key = f'{settings.CACHE_KEY_PREFIX}:{ip_address}:_facility_id'
# FACILITY_ID = cache.get(facility_key)


# FACILITY_ID = get_user_facility()
