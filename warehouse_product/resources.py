from .models import WarehouseProduct, ProductType, UnitOfMeasurement
from import_export import fields, resources
from import_export.widgets import ForeignKeyWidget
from django.contrib.auth.models import User
from django.core.exceptions import ObjectDoesNotExist
from status.models import Status
from warehouse.models import Warehouse
from product_category.models import ProductCategory


class GetOrCreateForeignKeyWidget(ForeignKeyWidget):
    """Widget that creates missing foreign key objects on the fly"""

    def clean(self, value, row=None, *args, **kwargs):
        if not value:
            return None

        try:
            return self.model.objects.get(**{self.field: value})
        except ObjectDoesNotExist:
            # Get current user for creating objects
            user = User.objects.filter(is_superuser=True).first()
            if not user:
                user = User.objects.first()

            # Get or create default status
            default_status, _ = Status.objects.get_or_create(
                name="ACTIVE",
                defaults={'created_by': user} if user else {}
            )

            # Create the missing object based on model type
            if self.model == ProductType:
                # Create default product category first
                default_category, _ = ProductCategory.objects.get_or_create(
                    name="General",
                    defaults={
                        'description': 'Default category for imports',
                        'created_by': user,
                        'status': default_status
                    } if user else {'description': 'Default category for imports'}
                )

                # Create the product type
                obj, created = ProductType.objects.get_or_create(
                    **{self.field: value},
                    defaults={
                        'product_category': default_category,
                        'description': f'Auto-created for {value}',
                        'created_by': user,
                        'status': default_status
                    } if user else {
                        'product_category': default_category,
                        'description': f'Auto-created for {value}'
                    }
                )
                return obj

            elif self.model == UnitOfMeasurement:
                # Create the unit of measurement
                obj, created = UnitOfMeasurement.objects.get_or_create(
                    **{self.field: value},
                    defaults={
                        'description': f'Auto-created for {value}',
                        'created_by': user,
                        'status': default_status
                    } if user else {'description': f'Auto-created for {value}'}
                )
                return obj

            # For other models, try basic creation
            return self.model.objects.create(**{self.field: value})


class WarehouseProductResource(resources.ModelResource):
    product_type = fields.Field(
        column_name="product_type",
        attribute="product_type",
        widget=GetOrCreateForeignKeyWidget(ProductType, "name"),
    )

    unit_of_measurement = fields.Field(
        column_name="unit_of_measurement",
        attribute="unit_of_measurement",
        widget=GetOrCreateForeignKeyWidget(UnitOfMeasurement, "name"),
    )

    class Meta:
        model = WarehouseProduct
        skip_unchanged = True
        report_skipped = False
        fields = "__all__"

    def before_import_row(self, row, **kwargs):
        """Set default values for required fields if not provided"""
        print("DEBUG: before_import_row called with row:", row)

        # Get current user
        user = User.objects.filter(is_superuser=True).first()
        if not user:
            user = User.objects.first()

        # Get or create default status
        default_status, _ = Status.objects.get_or_create(
            name="ACTIVE",
            defaults={'created_by': user} if user else {}
        )

        # Get default warehouse
        default_warehouse = Warehouse.objects.first()

        # Set default values for required fields if not provided
        if not row.get('warehouse') and default_warehouse:
            row['warehouse'] = default_warehouse.id
        if not row.get('created_by') and user:
            row['created_by'] = user.id
        if not row.get('status'):
            row['status'] = default_status.id
