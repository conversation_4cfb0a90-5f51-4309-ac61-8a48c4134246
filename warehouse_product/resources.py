from .models import WarehouseProduct, ProductType, UnitOfMeasurement
from import_export import fields, resources
from import_export.widgets import ForeignKeyWidget
from django.contrib.auth.models import User
from django.core.exceptions import ObjectDoesNotExist
from status.models import Status
from warehouse.models import Warehouse
from product_category.models import ProductCategory


class CustomForeignKeyWidget(ForeignKeyWidget):
    """Custom widget that creates missing foreign key objects"""

    def clean(self, value, row=None, *args, **kwargs):
        if not value:
            return None

        try:
            return self.model.objects.get(**{self.field: value})
        except ObjectDoesNotExist:
            # Object doesn't exist, it should be created in before_import_row
            # Try again after before_import_row has run
            try:
                return self.model.objects.get(**{self.field: value})
            except ObjectDoesNotExist:
                # Still doesn't exist, return None and let validation handle it
                return None


class WarehouseProductResource(resources.ModelResource):
    product_type = fields.Field(
        column_name="product_type",
        attribute="product_type",
        widget=CustomForeignKeyWidget(ProductType, "name"),
    )

    unit_of_measurement = fields.Field(
        column_name="unit_of_measurement",
        attribute="unit_of_measurement",
        widget=CustomForeignKeyWidget(UnitOfMeasurement, "name"),
    )

    class Meta:
        model = WarehouseProduct
        skip_unchanged = True
        report_skipped = False
        fields = "__all__"

    def before_import_row(self, row, **kwargs):
        print("DEBUG: before_import_row called with row:", row)

        # Get the current user from instance or kwargs
        user = self.user or kwargs.get('user')
        if not user:
            # Fallback to first superuser if no user provided
            user = User.objects.filter(is_superuser=True).first()
            if not user:
                raise ValueError("No user available for import. Please ensure you're logged in.")

        # Get or create default status
        default_status, _ = Status.objects.get_or_create(
            name="ACTIVE",
            defaults={'created_by': user}
        )

        # Get default warehouse (you may need to adjust this logic)
        default_warehouse = Warehouse.objects.first()
        if not default_warehouse:
            raise ValueError("No warehouse found. Please create at least one warehouse before importing.")

        # Handle product_type - get or create with required fields
        product_type_name = row.get('product_type')
        if product_type_name:
            # Get or create default product category
            default_category, _ = ProductCategory.objects.get_or_create(
                name="General",
                defaults={
                    'description': 'Default category for imports',
                    'created_by': user,
                    'status': default_status
                }
            )

            ProductType.objects.get_or_create(
                name=product_type_name,
                defaults={
                    'product_category': default_category,
                    'description': f'Auto-created for {product_type_name}',
                    'created_by': user,
                    'status': default_status
                }
            )

        # Handle unit_of_measurement - get or create with required fields
        unit_name = row.get('unit_of_measurement')
        if unit_name:
            UnitOfMeasurement.objects.get_or_create(
                name=unit_name,
                defaults={
                    'description': f'Auto-created for {unit_name}',
                    'created_by': user,
                    'status': default_status
                }
            )

        # Set default values for required fields if not provided
        if not row.get('warehouse'):
            row['warehouse'] = default_warehouse.id
        if not row.get('created_by'):
            row['created_by'] = user.id
        if not row.get('status'):
            row['status'] = default_status.id
