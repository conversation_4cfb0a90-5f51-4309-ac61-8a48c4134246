from .models import WarehouseProduct, ProductType, UnitOfMeasurement
from import_export import fields, resources
from import_export.widgets import ForeignKeyWidget


class WarehouseProductResource(resources.ModelResource):
    product_type = fields.Field(
        column_name="product_type",
        attribute="product_type",
        widget=ForeignKeyWidget(ProductType, "name"),
    )

    unit_of_measurement = fields.Field(
        column_name="unit_of_measurement",
        attribute="unit_of_measurement",
        widget=ForeignKeyWidget(UnitOfMeasurement, "name"),
    )

    class Meta:
        model = WarehouseProduct
        skip_unchanged = True
        report_skipped = False
        fields = "__all__"

    def before_import_row(self, row, **kwargs):
        breakpoint()
        print("DEBUG: before_import_row called with row:", row)
        # Handle product_type - get or create
        product_type_name = row.get('product_type')
        if product_type_name:
            ProductType.objects.get_or_create(name=product_type_name)

        # Handle unit_of_measurement - get or create
        unit_name = row.get('unit_of_measurement')
        if unit_name:
            UnitOfMeasurement.objects.get_or_create(name=unit_name)
