from django.contrib import admin

from import_export.admin import ImportExportModelAdmin

from warehouse_product.models import WarehouseProduct
from warehouse_product.resources import WarehouseProductResource


@admin.register(WarehouseProduct)
class WarehouseProductAdmin(ImportExportModelAdmin):
    resource_class = WarehouseProductResource
    list_display = [
        "name",
        "brand",
        "price",
        "unit_of_pricing",
        "level_of_prescribing",
        "description",
        "bar_code",
        "icd_code",
        "side_effect",
        "serial_number",
        "is_deleted",
    ]
    search_fields = ["name", "brand", "icd_code"]

    def get_import_resource_kwargs(self, request, *args, **kwargs):
        """Pass the current user to the import resource"""
        kwargs = super().get_import_resource_kwargs(request, *args, **kwargs)
        kwargs['user'] = request.user
        return kwargs

    def get_resource_class(self):
        """Ensure we use the correct resource class"""
        return WarehouseProductResource
