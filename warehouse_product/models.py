import typing
from datetime import date

from django.contrib.auth.models import User
from django.db import models
from django.db.models.signals import post_save
from django.dispatch import receiver
from product_type.models import ProductType
from sponsor.models import Sponsor
from status.models import Status
from unit_of_measurement.models import UnitOfMeasurement
from warehouse.models import Warehouse

if typing.TYPE_CHECKING:
    from warehouse_stock.models import WarehouseStock


class WarehouseProductManager(models.Manager):
    pass


class ExpiredProcutsManager(models.Manager):
    def get_queryset(self):
        return (
            super()
            .get_queryset()
            .filter(warehouse_product_id_in_stock__expiry_date__lte=date.today())
            .select_related("warehouse")
        )


class WarehouseOutletManager(models.Manager):
    def get_queryset(self, outlet=None):
        if outlet:
            return (
                super()
                .get_queryset()
                .filter(product_type__product_category__name__iexact=outlet)
                .select_related("product_type")
            )
        else:
            return super().get_queryset().select_related("product_type")


class WarehouseProduct(models.Model):
    warehouse_product_id_in_stock: models.QuerySet["WarehouseStock"]
    warehouse = models.ForeignKey(
        Warehouse,
        on_delete=models.CASCADE,
        related_name="warehouseId_in_Wharehouse_product",
        db_index=True,
    )

    product_type = models.ForeignKey(
        ProductType,
        on_delete=models.CASCADE,
        null=True, blank=True,

        related_name="product_type_in_Wharehouse",
    )
    name = models.CharField(
        max_length=100, help_text="Generic Name, dosage form, strength"
    )
    brand = models.TextField(max_length=100, null=True, blank=True)
    price = models.DecimalField(
        decimal_places=2, max_digits=10, default=0, help_text="NHIS Price"
    )
    # not in use
    unit_of_pricing = models.CharField(
        max_length=100, null=True, blank=True, help_text="By NHIS"
    )
    level_of_prescribing = models.CharField(
        max_length=200, null=True, blank=True, help_text="Facility Level"
    )

    description = models.TextField(max_length=100, null=True, blank=True)
    bar_code = models.TextField(max_length=200, null=True, blank=True)
    icd_code = models.TextField(max_length=100, null=True, blank=True)
    side_effect = models.TextField(max_length=100, null=True, blank=True)
    serial_number = models.TextField(
        max_length=100,
        null=True,
        blank=True,
    )

    strength_amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        null=True,
        help_text="Numeric strength (e.g., 250)",
    )
    strength_unit = models.CharField(
        max_length=20, null=True, help_text="Unit of strength (e.g., 'mg')"
    )

    sponsor = models.ForeignKey(
        Sponsor, on_delete=models.CASCADE, null=True, blank=True
    )

    unit_of_measurement = models.ForeignKey(
        UnitOfMeasurement,
        on_delete=models.CASCADE,
        related_name="unit_of_measurement_type_in_warehouse_product",
    )
    created_by = models.ForeignKey(
        User, on_delete=models.CASCADE, related_name="warehouse_product_user"
    )
    created_on = models.DateTimeField(auto_now_add=True)
    modified_on = models.DateTimeField(auto_now=True)
    is_deleted = models.BooleanField(default=False)
    status = models.ForeignKey(Status, on_delete=models.CASCADE)

    def __str__(self):
        return f"{self.name}"

    @property
    def patient_product_price(self):
        for values in self.product_with_price.all():
            return values.price

    @property
    def sponsor_product_price(self):
        for values in self.product_with_price_sponsor.all():
            return values.price

    @property
    def warehouseproduct_prices(self):
        return {
            "patient_price": self.patient_product_price
            if self.patient_product_price is not None
            else 0,
            "sponsor_price": self.sponsor_product_price
            if self.sponsor_product_price is not None
            else 0,
        }

    objects = models.Manager()
    expired_warehouse_products = ExpiredProcutsManager()
    warehouse_outlet_products = WarehouseOutletManager()
    warehouse_product = WarehouseProductManager()


@receiver(post_save, sender=WarehouseProduct)
def create_product_price(sender, instance: WarehouseProduct, created, **kwargs):
    from product_price.models import ProductPrice

    if created:
        ProductPrice.objects.create(
            product=instance,
            cost_price=instance.price or instance.strength_amount,
            price_per_unit=instance.unit_of_pricing or instance.price,
            created_by=instance.created_by,
            status=instance.status,
        )
