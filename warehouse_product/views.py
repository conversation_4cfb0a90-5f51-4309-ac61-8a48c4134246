# Create your views here.
from datetime import date

from django.db.models import Q
from rest_framework import status
from rest_framework import viewsets
from rest_framework.decorators import action
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.views import APIView

from facility_category.models import FacilityCategory
from .models import WarehouseProduct
from .serializers import CombinedWarehouseProductSerializer
from .serializers import WarehouseProductSerializer


class WarehouseProductViewSet(viewsets.ModelViewSet):
    """
        Filter products based on facility category hierarchy.
        Returns products accessible to user's facility level or lower.
    """
    permission_classes = [IsAuthenticated]
    serializer_class = WarehouseProductSerializer

    def get_queryset(self):

        user = self.request.user
        user_facility_position = user.facility.category.position or 0

        allowed_levels = FacilityCategory.objects.filter(
            position__lte=user_facility_position
        ).values_list('level', flat=True)

        queryset = WarehouseProduct.objects.filter(
            Q(level_of_prescribing__in=allowed_levels)
            | Q(level_of_prescribing__isnull=True)
            | Q(level_of_prescribing="")
        ).order_by("-created_on")

        return queryset

    @action(detail=True, methods=["get"])
    def check_availability(self, request, pk=None):
        try:
            product = self.get_object()
            today = date.today()
            stock_qs = product.warehouse_product_id_in_stock.select_related(
                "warehouse_product"
            ).all()

            available_stock = (
                stock_qs.filter(quantity__gt=0, expiry_date__gte=today)
                .order_by("expiry_date")
                .first()
            )

            if available_stock:
                return Response(
                    {
                        "is_available": True,
                        "message": f"available  quantity ({available_stock.quantity})",
                    }
                )

            if stock_qs.filter(quantity__gt=0, expiry_date__lt=today).exists():
                return Response(
                    {"is_available": False, "message": "out of stock (expired)"}
                )

            if stock_qs.filter(quantity=0).exists():
                return Response({"is_available": False, "message": "out of stock"})

            return Response({"is_available": False, "message": "no stock records"})

        except Exception as e:
            return Response(
                {
                    "is_available": False,
                    "message": f"Error checking availability: {str(e)}",
                },
                status=status.HTTP_400_BAD_REQUEST,
            )


class CombinedWarehouseProductView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request, format=None):
        user = self.request.user
        facility_level = user.facility.category.level
        queryset = WarehouseProduct.objects.filter(
            Q(level_of_prescribing=facility_level)
            | Q(level_of_prescribing__isnull=True)
            | Q(level_of_prescribing="")
        ).order_by("-created_on")
        serialized = CombinedWarehouseProductSerializer(queryset, many=True)
        return Response(serialized.data)
