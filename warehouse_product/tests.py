from django.test import TestCase
from django.contrib.auth.models import User
from io import StringIO
from tablib import Dataset
from warehouse_product.resources import WarehouseProductResource
from warehouse_product.models import WarehouseProduct, ProductType, UnitOfMeasurement
from warehouse.models import Warehouse
from status.models import Status
from product_category.models import ProductCategory


class WarehouseProductResourceTest(TestCase):
    def setUp(self):
        # Create test user
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )

        # Create test status
        self.status = Status.objects.create(
            name='ACTIVE',
            created_by=self.user
        )

        # Create test warehouse
        self.warehouse = Warehouse.objects.create(
            name='Test Warehouse',
            location='Test Location',
            created_by=self.user,
            status=self.status
        )

    def test_import_with_new_product_type_and_unit(self):
        """Test importing warehouse product with new product type and unit of measurement"""
        resource = WarehouseProductResource(user=self.user)

        # Create test data
        dataset = Dataset()
        dataset.headers = [
            'name', 'product_type', 'unit_of_measurement', 'price',
            'warehouse', 'created_by', 'status'
        ]
        dataset.append([
            'Test Medicine 500mg',
            'Tablet',  # New product type
            'mg',      # New unit of measurement
            10.50,
            self.warehouse.id,
            self.user.id,
            self.status.id
        ])

        # Import the data
        result = resource.import_data(dataset, dry_run=False)

        # Verify import was successful
        self.assertFalse(result.has_errors())

        # Verify product was created
        product = WarehouseProduct.objects.get(name='Test Medicine 500mg')
        self.assertEqual(product.price, 10.50)

        # Verify product type was created
        product_type = ProductType.objects.get(name='Tablet')
        self.assertEqual(product.product_type, product_type)

        # Verify unit of measurement was created
        unit = UnitOfMeasurement.objects.get(name='mg')
        self.assertEqual(product.unit_of_measurement, unit)

        # Verify product category was created for the product type
        self.assertTrue(ProductCategory.objects.filter(name='General').exists())

    def test_import_with_existing_product_type_and_unit(self):
        """Test importing warehouse product with existing product type and unit"""
        # Create existing product category
        category = ProductCategory.objects.create(
            name='Medicines',
            created_by=self.user,
            status=self.status
        )

        # Create existing product type
        existing_product_type = ProductType.objects.create(
            name='Capsule',
            product_category=category,
            created_by=self.user,
            status=self.status
        )

        # Create existing unit of measurement
        existing_unit = UnitOfMeasurement.objects.create(
            name='ml',
            created_by=self.user,
            status=self.status
        )

        resource = WarehouseProductResource(user=self.user)

        # Create test data with existing types
        dataset = Dataset()
        dataset.headers = [
            'name', 'product_type', 'unit_of_measurement', 'price',
            'warehouse', 'created_by', 'status'
        ]
        dataset.append([
            'Test Syrup 100ml',
            'Capsule',  # Existing product type
            'ml',       # Existing unit of measurement
            25.00,
            self.warehouse.id,
            self.user.id,
            self.status.id
        ])

        # Import the data
        result = resource.import_data(dataset, dry_run=False)

        # Verify import was successful
        self.assertFalse(result.has_errors())

        # Verify product was created with existing relationships
        product = WarehouseProduct.objects.get(name='Test Syrup 100ml')
        self.assertEqual(product.product_type, existing_product_type)
        self.assertEqual(product.unit_of_measurement, existing_unit)

        # Verify no duplicate types were created
        self.assertEqual(ProductType.objects.filter(name='Capsule').count(), 1)
        self.assertEqual(UnitOfMeasurement.objects.filter(name='ml').count(), 1)
